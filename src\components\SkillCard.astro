---
import type { SkillData } from '@/env.d.ts';

export interface Props {
  skill: SkillData;
  index?: number;
}

const { skill, index = 0 } = Astro.props;

// Category colors mapping
const categoryColors = {
  accounting: 'from-green-400 to-emerald-600',
  design: 'from-pink-400 to-rose-600', 
  data: 'from-blue-400 to-indigo-600',
  web: 'from-cyan-400 to-teal-600',
  tools: 'from-yellow-400 to-orange-600',
  soft: 'from-purple-400 to-violet-600'
} as const;

const categoryIcons = {
  accounting: '💰',
  design: '🎨',
  data: '📊',
  web: '💻',
  tools: '🛠️',
  soft: '🤝'
} as const;

const gradientClass = categoryColors[skill.category] || categoryColors.data;
const icon = categoryIcons[skill.category] || categoryIcons.data;
---

<div 
  class="skill-card bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-lg p-6 hover:border-cyan-400/60 transition-all duration-300 hover:shadow-lg hover:shadow-cyan-400/20"
  style={`animation-delay: ${index * 0.1}s`}
  role="article"
  aria-labelledby={`skill-${skill.name.replace(/\s+/g, '-').toLowerCase()}`}
>
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center gap-3">
      <span class="text-2xl" role="img" aria-label={`${skill.category} category`}>
        {icon}
      </span>
      <h3 
        id={`skill-${skill.name.replace(/\s+/g, '-').toLowerCase()}`}
        class="text-lg font-semibold text-white"
      >
        {skill.name}
      </h3>
    </div>
    <span 
      class="text-sm font-mono text-cyan-400 bg-cyan-400/10 px-2 py-1 rounded"
      aria-label={`Skill level: ${skill.level} percent`}
    >
      {skill.level}%
    </span>
  </div>
  
  <div class="space-y-2">
    <div class="flex justify-between text-sm text-gray-400">
      <span>Progress</span>
      <span>{skill.level}%</span>
    </div>
    <div 
      class="w-full bg-gray-700 rounded-full h-2 overflow-hidden"
      role="progressbar"
      aria-valuenow={skill.level}
      aria-valuemin={0}
      aria-valuemax={100}
      aria-label={`${skill.name} skill progress: ${skill.level}%`}
    >
      <div 
        class={`h-full bg-gradient-to-r ${gradientClass} rounded-full skill-progress transition-all duration-1000 ease-out`}
        style={`width: ${skill.level}%`}
      ></div>
    </div>
  </div>
</div>

<style>
  .skill-card {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .skill-progress {
    width: 0;
    animation: fillProgress 1.5s ease-out forwards;
  }
  
  @keyframes fillProgress {
    to {
      width: var(--skill-level, 0%);
    }
  }
</style>

<script>
  // Intersection Observer for skill progress animation
  const skillCards = document.querySelectorAll('.skill-card');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const progressBar = entry.target.querySelector('.skill-progress') as HTMLElement;
        if (progressBar) {
          const skillLevel = progressBar.parentElement?.getAttribute('aria-valuenow');
          if (skillLevel) {
            progressBar.style.setProperty('--skill-level', `${skillLevel}%`);
            progressBar.style.animation = 'fillProgress 1.5s ease-out forwards';
          }
        }
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.3,
    rootMargin: '0px 0px -50px 0px'
  });
  
  skillCards.forEach((card) => observer.observe(card));
</script>
