// @ts-check
import { defineConfig } from "astro/config";
import tailwind from "@astrojs/tailwind";
import react from "@astrojs/react";

// https://astro.build/config
export default defineConfig({
  site: "https://trinanda-portfolio.vercel.app",
  integrations: [
    tailwind({
      applyBaseStyles: false, // We have custom base styles
    }),
    react(),
  ],
  vite: {
    optimizeDeps: {
      include: ["three", "gsap", "lenis"],
    },
    build: {
      cssCodeSplit: true,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["three", "gsap", "lenis"],
          },
        },
      },
    },
  },
  build: {
    inlineStylesheets: "auto",
  },
  prefetch: {
    prefetchAll: true,
    defaultStrategy: "viewport",
  },
  experimental: {
    contentIntellisense: true,
  },
});
