{"css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [".vscode/css_custom_data.json"], "tailwindCSS.includeLanguages": {"astro": "html"}, "tailwindCSS.experimental.classRegex": [["class:list=\\{([^}]*)\\}", "'([^']*)'"], ["class:list=\\{([^}]*)\\}", "\"([^\"]*)\""], ["class=\\{([^}]*)\\}", "'([^']*)'"], ["class=\\{([^}]*)\\}", "\"([^\"]*)\""]], "files.associations": {"*.astro": "astro"}}