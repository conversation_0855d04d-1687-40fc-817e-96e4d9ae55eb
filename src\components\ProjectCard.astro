---
import type { ProjectData } from '@/env.d.ts';

export interface Props {
  project: ProjectData;
  index?: number;
}

const { project, index = 0 } = Astro.props;

// Category colors for projects
const categoryColors = {
  dashboard: 'from-blue-500 to-cyan-500',
  design: 'from-pink-500 to-rose-500',
  analysis: 'from-green-500 to-emerald-500',
  web: 'from-purple-500 to-violet-500',
  accounting: 'from-yellow-500 to-orange-500',
  visualization: 'from-indigo-500 to-blue-500'
} as const;

const gradientClass = categoryColors[project.category] || categoryColors.dashboard;
---

<article 
  class="project-card group bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-xl overflow-hidden hover:border-cyan-400/60 transition-all duration-300 hover:shadow-xl hover:shadow-cyan-400/20 hover:-translate-y-2"
  style={`animation-delay: ${index * 0.2}s`}
  aria-labelledby={`project-${project.id}`}
>
  <!-- Project Image -->
  <div class="relative overflow-hidden h-48 bg-gradient-to-br from-gray-800 to-gray-900">
    {project.image ? (
      <img 
        src={project.image} 
        alt={`${project.title} project screenshot`}
        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        loading="lazy"
        decoding="async"
      />
    ) : (
      <div class={`w-full h-full bg-gradient-to-br ${gradientClass} flex items-center justify-center`}>
        <div class="text-6xl opacity-20">
          {project.category === 'dashboard' && '📊'}
          {project.category === 'design' && '🎨'}
          {project.category === 'analysis' && '📈'}
          {project.category === 'web' && '💻'}
          {project.category === 'accounting' && '💰'}
          {project.category === 'visualization' && '📉'}
        </div>
      </div>
    )}
    
    <!-- Category Badge -->
    <div class="absolute top-4 left-4">
      <span class={`px-3 py-1 text-xs font-semibold text-white bg-gradient-to-r ${gradientClass} rounded-full shadow-lg`}>
        {project.category.toUpperCase()}
      </span>
    </div>
  </div>
  
  <!-- Project Content -->
  <div class="p-6 space-y-4">
    <header>
      <h3 
        id={`project-${project.id}`}
        class="text-xl font-bold text-white group-hover:text-cyan-400 transition-colors"
      >
        {project.title}
      </h3>
    </header>
    
    <p class="text-gray-300 text-sm leading-relaxed line-clamp-3">
      {project.description}
    </p>
    
    <!-- Tags -->
    <div class="flex flex-wrap gap-2" role="list" aria-label="Project technologies">
      {project.tags.map((tag) => (
        <span 
          class="px-2 py-1 text-xs bg-cyan-400/10 text-cyan-400 rounded border border-cyan-400/20 hover:bg-cyan-400/20 transition-colors"
          role="listitem"
        >
          {tag}
        </span>
      ))}
    </div>
    
    <!-- Action Links -->
    <div class="flex gap-3 pt-2" role="group" aria-label="Project links">
      {project.links.demo && (
        <a 
          href={project.links.demo}
          class="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium text-center hover:from-cyan-400 hover:to-blue-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-gray-900"
          aria-label={`View ${project.title} demo`}
        >
          View Demo
        </a>
      )}
      
      {project.links.github && (
        <a 
          href={project.links.github}
          class="px-4 py-2 border border-gray-600 text-gray-300 rounded-lg text-sm font-medium hover:border-gray-500 hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-gray-900"
          aria-label={`View ${project.title} source code on GitHub`}
        >
          GitHub
        </a>
      )}
      
      {project.links.live && (
        <a 
          href={project.links.live}
          class="px-4 py-2 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-lg text-sm font-medium hover:from-pink-400 hover:to-rose-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2 focus:ring-offset-gray-900"
          aria-label={`Visit live ${project.title} website`}
        >
          Live Site
        </a>
      )}
    </div>
  </div>
</article>

<style>
  .project-card {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
  }
  
  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
