---
import type { LayoutProps } from '@/env.d.ts';
import '@/styles/global.css';

export interface Props extends LayoutProps {}

const {
	title,
	description = "Portfolio interaktif Muhammad Trinanda - Cyberpunk Edition. Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain.",
	image = "/og-image.jpg",
	canonical
} = Astro.props;

// Generate canonical URL
const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
const currentYear = new Date().getFullYear();
---

<!doctype html>
<html lang="id">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="theme-color" content="#00ffff" />
		<meta name="color-scheme" content="dark" />

		<!-- Canonical URL -->
		<link rel="canonical" href={canonicalURL} />

		<!-- Favicon and Icons -->
		<link rel="icon" type="image/x-icon" href="/favicon.ico" />
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
		<link rel="manifest" href="/site.webmanifest" />

		<!-- SEO Meta Tags -->
		<meta name="generator" content={Astro.generator} />
		<meta name="author" content="Muhammad Trinanda" />
		<meta name="keywords" content="Muhammad Trinanda, Portfolio, Akuntansi Syariah, Data Analysis, Web Design, Cyberpunk, Dashboard, Visualization" />
		<meta name="robots" content="index, follow" />
		<meta name="language" content="Indonesian" />
		<meta name="copyright" content={`© ${currentYear} Muhammad Trinanda`} />

		<title>{title}</title>

		<!-- Preload critical resources -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />

		<!-- Open Graph Meta Tags -->
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		<meta property="og:type" content="website" />
		<meta property="og:url" content={canonicalURL} />
		<meta property="og:image" content={new URL(image, Astro.site)} />
		<meta property="og:image:alt" content={`${title} - Portfolio Screenshot`} />
		<meta property="og:site_name" content="Muhammad Trinanda Portfolio" />
		<meta property="og:locale" content="id_ID" />

		<!-- Twitter Card Meta Tags -->
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content={title} />
		<meta name="twitter:description" content={description} />
		<meta name="twitter:image" content={new URL(image, Astro.site)} />
		<meta name="twitter:image:alt" content={`${title} - Portfolio Screenshot`} />

		<!-- Structured Data -->
		<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "Person",
				"name": "Muhammad Trinanda",
				"jobTitle": "Mahasiswa Akuntansi Syariah",
				"description": "Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain",
				"url": "https://trinanda-portfolio.vercel.app",
				"sameAs": [
					"https://linkedin.com/in/muhammad-trinanda",
					"https://instagram.com/trinanda.id"
				],
				"knowsAbout": ["Akuntansi Syariah", "Data Analysis", "Web Design", "Dashboard Design", "Data Visualization"],
				"alumniOf": {
					"@type": "EducationalOrganization",
					"name": "Universitas"
				}
			}
		</script>
	</head>
	<body class="cyber-grid">
		<!-- Particle Background -->
		<div id="particles-bg" class="particles-bg"></div>

		<!-- Matrix Rain Background -->
		<canvas id="matrix-canvas" class="fixed inset-0 -z-10 pointer-events-none opacity-10"></canvas>

		<!-- Main Content -->
		<div class="relative z-10">
			<slot />
		</div>

		<!-- Matrix Rain Script -->
		<script>
			// Matrix Rain Effect
			const canvas = document.getElementById('matrix-canvas');
			if (canvas) {
				const ctx = canvas.getContext('2d');

				function resizeCanvas() {
					canvas.width = window.innerWidth;
					canvas.height = window.innerHeight;
				}

				resizeCanvas();

				const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
				const matrixArray = matrix.split("");

				const fontSize = 10;
				let columns = canvas.width / fontSize;

				let drops = [];
				for(let x = 0; x < columns; x++) {
					drops[x] = 1;
				}

				function drawMatrix() {
					ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
					ctx.fillRect(0, 0, canvas.width, canvas.height);

					ctx.fillStyle = '#00ff41';
					ctx.font = fontSize + 'px monospace';

					for(let i = 0; i < drops.length; i++) {
						const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
						ctx.fillText(text, i * fontSize, drops[i] * fontSize);

						if(drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
							drops[i] = 0;
						}
						drops[i]++;
					}
				}

				setInterval(drawMatrix, 35);

				// Resize handler
				window.addEventListener('resize', () => {
					resizeCanvas();
					columns = canvas.width / fontSize;
					drops = [];
					for(let x = 0; x < columns; x++) {
						drops[x] = 1;
					}
				});
			}
		</script>
	</body>
</html>
